import Image from 'next/image'

import { Media, Product } from '@/payload-types'
import { APP_ROUTES } from '@/routes'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'

type ProductItemType = {
  isNew?: boolean
  productData: Product
  bgDefault?: string
  onClick?: () => void
}

const ProductItem: React.FC<ProductItemType> = ({
  isNew = false,
  productData,
  bgDefault = 'bg-custom-background-hover',
  onClick,
}) => {
  if (!productData) return null
  const { stores, title, heroImage } = productData
  const searchParams = useSearchParams()

  const banner = (heroImage as Media) || {}

  const storeIcons = stores?.map((store) => store['medicine-store']?.['logo'])

  // Lấy tất cả các param hiện tại từ URL
  const currentParams = new URLSearchParams(searchParams.toString())
  // Thêm id của product vào params
  currentParams.set('id', productData.id)
  // Tạo URL với tất cả params được bảo toàn
  const productUrl = `${APP_ROUTES.PRODUCTS_V2.path}/${productData.slug}?${currentParams.toString()}`

  return (
    <Link onClick={onClick} href={productUrl}>
      <div
        className={`flex min-h-[230px] cursor-pointer flex-col gap-2 rounded-lg px-3 py-4 ${isNew ? 'bg-yellow-100' : bgDefault}`}
      >
        {/* Related */}
        <div className="flex min-h-5 items-center gap-3">
          {storeIcons?.map((store) => {
            const iconURL = store?.sizes?.thumbnail?.url || store?.url
            return (
              iconURL && (
                <div className="relative h-[18px] w-[18px]" key={store?.id}>
                  <Image key={store.id} alt={'logo'} src={iconURL} height={20} width={19} />
                </div>
              )
            )
          })}
        </div>

        <div className="relative h-[120px] w-full">
          {banner.url && (
            <Image src={banner.url} alt={'label'} fill className="h-full w-full object-cover" />
          )}
        </div>

        <div className="typo-body-6 line-clamp-2">{title}</div>
      </div>
    </Link>
  )
}

export default ProductItem
